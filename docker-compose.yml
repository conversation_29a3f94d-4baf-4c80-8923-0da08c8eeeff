version: '3.8'

services:
  # Backend service (single instance)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: chat-system-backend
    ports:
      - "8020:8000"  # Map host port 8020 to container port 8000
    environment:
      # MongoDB connection - using external MongoDB
      - MONGO_URI=${MONGO_URI:-mongodb://localhost:27017/}
      - DATABASE_NAME=${DATABASE_NAME:-chatbot_system}

      # App configuration
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1

      # Optional: Add other environment variables
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
    # volumes:
      # Mount for development (optional) - disabled to avoid read-only issues
      # - ./backend:/app:ro
    networks:
      - chat-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend service (single instance)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: chat-system-frontend
    ports:
      - "8201:3000"  # Map host port 8201 to container port 3000
    environment:
      # Backend API URL - point to one of the backend replicas
      - VITE_API_BASE_URL=http://localhost:8020
      - VITE_API_TIMEOUT=10000
      - VITE_APP_NAME=Chat System
      - VITE_APP_VERSION=1.0.0
      - VITE_DEV_MODE=false
    depends_on:
      - backend
    networks:
      - chat-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  chat-network:
    driver: bridge
    name: chat-system-network

# Optional: Add volumes for persistent data if needed
volumes:
  backend_data:
    name: chat-system-backend-data
