/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'primary': ['Inter', 'system-ui', 'sans-serif'],
        'secondary': ['Inter', 'system-ui', 'sans-serif'],
      },
      colors: {
        // Monochrome design with customizable accent
        'accent': {
          'main': 'var(--accent-color)',
          'dark': 'var(--accent-color-dark)',
          'light': 'var(--accent-color-light)',
        },
        'text': {
          'primary': '#000000',
          'secondary': '#4a4a4a',
          'light': '#8a8a8a',
        },
        'bg': {
          'primary': '#ffffff',
          'secondary': '#f8f9fa',
          'tertiary': '#f1f3f4',
        },
        'border': '#e1e5e9',
      },
    },
  },
  plugins: [],
}

