/**
 * Main App component with routing
 */
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import store from './store';

// Simple test components first
const LoginPage = () => (
  <div className="min-h-screen bg-white flex items-center justify-center">
    <div className="max-w-md w-full space-y-8 p-8">
      <h2 className="text-3xl font-bold text-center text-black">Sign In</h2>
      <form className="space-y-6">
        <div>
          <input
            type="email"
            placeholder="Email"
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
        <div>
          <input
            type="password"
            placeholder="Password"
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
        <button
          type="submit"
          className="w-full bg-black text-white py-2 px-4 rounded-md hover:bg-gray-800"
        >
          Sign In
        </button>
      </form>
      <p className="text-center">
        Don't have an account?{' '}
        <a href="/signup" className="text-blue-600 hover:underline">
          Sign up
        </a>
      </p>
    </div>
  </div>
);

const SignupPage = () => (
  <div className="min-h-screen bg-white flex items-center justify-center">
    <div className="max-w-md w-full space-y-8 p-8">
      <h2 className="text-3xl font-bold text-center text-black">Sign Up</h2>
      <form className="space-y-6">
        <div>
          <input
            type="text"
            placeholder="Full Name"
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
        <div>
          <input
            type="email"
            placeholder="Email"
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
        <div>
          <input
            type="tel"
            placeholder="Phone"
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
        <div>
          <input
            type="password"
            placeholder="Password"
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
        <button
          type="submit"
          className="w-full bg-black text-white py-2 px-4 rounded-md hover:bg-gray-800"
        >
          Sign Up
        </button>
      </form>
      <p className="text-center">
        Already have an account?{' '}
        <a href="/login" className="text-blue-600 hover:underline">
          Sign in
        </a>
      </p>
    </div>
  </div>
);

const DashboardLayout = ({ children }: { children: React.ReactNode }) => (
  <div className="min-h-screen bg-gray-50 flex">
    {/* Sidebar */}
    <div className="w-64 bg-white shadow-lg">
      <div className="p-6">
        <h1 className="text-xl font-bold text-black">Chat System</h1>
      </div>
      <nav className="mt-6">
        <a href="/dashboard" className="block px-6 py-3 text-gray-700 hover:bg-gray-100">
          Dashboard
        </a>
        <a href="/chat" className="block px-6 py-3 text-gray-700 hover:bg-gray-100">
          Chat
        </a>
        <a href="/booking" className="block px-6 py-3 text-gray-700 hover:bg-gray-100">
          Booking
        </a>
        <a href="/logout" className="block px-6 py-3 text-gray-700 hover:bg-gray-100">
          Logout
        </a>
      </nav>
    </div>

    {/* Main content */}
    <div className="flex-1 p-8">
      {children}
    </div>
  </div>
);

const ChatPage = () => (
  <DashboardLayout>
    <div className="max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold text-black mb-6">Chat</h2>
      <div className="bg-white rounded-lg shadow p-6">
        <div className="h-96 border border-gray-200 rounded p-4 mb-4 overflow-y-auto">
          <p className="text-gray-500">Chat messages will appear here...</p>
        </div>
        <div className="flex space-x-2">
          <input
            type="text"
            placeholder="Type your message..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
          />
          <button className="bg-black text-white px-6 py-2 rounded-md hover:bg-gray-800">
            Send
          </button>
        </div>
      </div>
    </div>
  </DashboardLayout>
);

function App() {
  return (
    <Provider store={store}>
      <Router>
        <div className="App">
          <Routes>
            {/* Auth routes */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/signup" element={<SignupPage />} />

            {/* Protected routes */}
            <Route path="/chat" element={<ChatPage />} />
            <Route path="/dashboard" element={<DashboardLayout><div>Dashboard Content</div></DashboardLayout>} />
            <Route path="/booking" element={<DashboardLayout><div>Booking Content</div></DashboardLayout>} />

            {/* Default redirect */}
            <Route path="/" element={<Navigate to="/login" replace />} />

            {/* Catch all - redirect to login */}
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        </div>
      </Router>
    </Provider>
  );
}

export default App;
