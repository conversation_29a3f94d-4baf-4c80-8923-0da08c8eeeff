/**
 * Authentication service
 * Handles login, logout, registration, and session management
 */
import { httpBase, ApiCallbacks } from './baseHttp';
import { LoginRequest, RegisterRequest, AuthResponse, User } from '../types/api';

class AuthService {
  private readonly AUTH_ENDPOINTS = {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REGISTER: '/api/auth/register',
    ME: '/api/auth/me',
    REFRESH: '/api/auth/refresh',
  };

  /**
   * Login user with email and password
   */
  async login(
    credentials: LoginRequest,
    callbacks?: ApiCallbacks<AuthResponse>
  ): Promise<AuthResponse | null> {
    try {
      const response = await httpBase.post<AuthResponse>(
        this.AUTH_ENDPOINTS.LOGIN,
        credentials,
        {},
        {
          onSuccess: (data) => {
            console.log('✅ Login successful:', data.user.name);
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ <PERSON>gin failed:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Register new user
   */
  async register(
    userData: RegisterRequest,
    callbacks?: ApiCallbacks<AuthResponse>
  ): Promise<AuthResponse | null> {
    try {
      const response = await httpBase.post<AuthResponse>(
        this.AUTH_ENDPOINTS.REGISTER,
        userData,
        {},
        {
          onSuccess: (data) => {
            console.log('✅ Registration successful:', data.user.name);
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Registration failed:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Logout current user
   */
  async logout(callbacks?: ApiCallbacks<{ message: string }>): Promise<boolean> {
    try {
      await httpBase.post(
        this.AUTH_ENDPOINTS.LOGOUT,
        {},
        {},
        {
          onSuccess: (data) => {
            console.log('✅ Logout successful');
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Logout failed:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(callbacks?: ApiCallbacks<User>): Promise<User | null> {
    try {
      const response = await httpBase.get<User>(
        this.AUTH_ENDPOINTS.ME,
        {},
        {
          onSuccess: (data) => {
            console.log('✅ User profile loaded:', data.name);
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to load user profile:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Refresh user session
   */
  async refreshSession(callbacks?: ApiCallbacks<AuthResponse>): Promise<AuthResponse | null> {
    try {
      const response = await httpBase.post<AuthResponse>(
        this.AUTH_ENDPOINTS.REFRESH,
        {},
        {},
        {
          onSuccess: (data) => {
            console.log('✅ Session refreshed');
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Session refresh failed:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if user is authenticated (client-side check)
   */
  isAuthenticated(): boolean {
    // This is a simple client-side check
    // The real authentication is handled by the backend session
    return document.cookie.includes('session_id');
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate password strength
   */
  validatePassword(password: string): { isValid: boolean; message: string } {
    if (password.length < 8) {
      return { isValid: false, message: 'Password must be at least 8 characters long' };
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one lowercase letter' };
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one uppercase letter' };
    }
    if (!/(?=.*\d)/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one number' };
    }
    return { isValid: true, message: 'Password is valid' };
  }

  /**
   * Validate phone number format
   */
  validatePhone(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,15}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
