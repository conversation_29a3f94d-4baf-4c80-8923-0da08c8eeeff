/**
 * Dashboard service
 * Handles analytics, statistics, and dashboard data
 */
import { httpBase, ApiCallbacks } from './baseHttp';
import { DashboardStats, HealthResponse } from '../types/api';

class DashboardService {
  private readonly DASHBOARD_ENDPOINTS = {
    STATS: '/api/dashboard/stats',
    HEALTH: '/health',
    SYSTEM_INFO: '/api/dashboard/system',
  };

  /**
   * Get dashboard statistics
   */
  async getDashboardStats(
    callbacks?: ApiCallbacks<DashboardStats>
  ): Promise<DashboardStats | null> {
    try {
      const response = await httpBase.get<DashboardStats>(
        this.DASHBOARD_ENDPOINTS.STATS,
        {},
        {
          onSuccess: (data) => {
            console.log('✅ Dashboard stats loaded');
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to load dashboard stats:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get system health status
   */
  async getHealthStatus(
    callbacks?: ApiCallbacks<HealthResponse>
  ): Promise<HealthResponse | null> {
    try {
      const response = await httpBase.get<HealthResponse>(
        this.DASHBOARD_ENDPOINTS.HEALTH,
        {},
        {
          onSuccess: (data) => {
            console.log(`✅ System health: ${data.status}`);
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to check system health:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Format large numbers for display
   */
  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  /**
   * Calculate percentage change
   */
  calculatePercentageChange(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }

  /**
   * Format percentage for display
   */
  formatPercentage(percentage: number): string {
    const sign = percentage >= 0 ? '+' : '';
    return `${sign}${percentage.toFixed(1)}%`;
  }

  /**
   * Get status color based on health
   */
  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'healthy':
        return 'text-success';
      case 'warning':
        return 'text-warning';
      case 'unhealthy':
      case 'error':
        return 'text-error';
      default:
        return 'text-text-secondary';
    }
  }

  /**
   * Get status icon based on health
   */
  getStatusIcon(status: string): string {
    switch (status.toLowerCase()) {
      case 'healthy':
        return 'check-circle';
      case 'warning':
        return 'alert-triangle';
      case 'unhealthy':
      case 'error':
        return 'x-circle';
      default:
        return 'help-circle';
    }
  }

  /**
   * Format uptime duration
   */
  formatUptime(timestamp: string): string {
    const startTime = new Date(timestamp);
    const now = new Date();
    const diffInMs = now.getTime() - startTime.getTime();
    
    const days = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diffInMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diffInMs % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  /**
   * Get trend direction
   */
  getTrendDirection(current: number, previous: number): 'up' | 'down' | 'stable' {
    const change = this.calculatePercentageChange(current, previous);
    if (Math.abs(change) < 1) return 'stable';
    return change > 0 ? 'up' : 'down';
  }

  /**
   * Get trend icon
   */
  getTrendIcon(direction: 'up' | 'down' | 'stable'): string {
    switch (direction) {
      case 'up':
        return 'trending-up';
      case 'down':
        return 'trending-down';
      case 'stable':
        return 'minus';
      default:
        return 'minus';
    }
  }

  /**
   * Get trend color
   */
  getTrendColor(direction: 'up' | 'down' | 'stable', isPositive: boolean = true): string {
    if (direction === 'stable') return 'text-text-secondary';
    
    const isGoodTrend = (direction === 'up' && isPositive) || (direction === 'down' && !isPositive);
    return isGoodTrend ? 'text-success' : 'text-error';
  }
}

// Export singleton instance
export const dashboardService = new DashboardService();
export default dashboardService;
