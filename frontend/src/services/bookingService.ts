/**
 * Booking service
 * Handles appointment booking, slot management, and booking history
 */
import { httpBase, ApiCallbacks } from './baseHttp';
import { TimeSlot, BookingRequest, Booking, ServiceType } from '../types/api';

class BookingService {
  private readonly BOOKING_ENDPOINTS = {
    GET_SLOTS: '/api/booking/slots',
    CREATE_BOOKING: '/api/booking',
    GET_BOOKINGS: '/api/booking/user',
    UPDATE_BOOKING: '/api/booking',
    CANCEL_BOOKING: '/api/booking',
  };

  /**
   * Get available time slots
   */
  async getAvailableSlots(
    daysAhead: number = 14,
    callbacks?: ApiCallbacks<TimeSlot[]>
  ): Promise<TimeSlot[] | null> {
    try {
      const response = await httpBase.get<TimeSlot[]>(
        this.BOOKING_ENDPOINTS.GET_SLOTS,
        { params: { days_ahead: daysAhead } },
        {
          onSuccess: (data) => {
            console.log(`✅ Loaded ${data.length} available slots`);
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to load available slots:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Create a new booking
   */
  async createBooking(
    bookingData: BookingRequest,
    callbacks?: ApiCallbacks<Booking>
  ): Promise<Booking | null> {
    try {
      const response = await httpBase.post<Booking>(
        this.BOOKING_ENDPOINTS.CREATE_BOOKING,
        bookingData,
        {},
        {
          onSuccess: (data) => {
            console.log(`✅ Booking created successfully: ${data.booking_id}`);
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to create booking:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get user's bookings
   */
  async getUserBookings(
    callbacks?: ApiCallbacks<Booking[]>
  ): Promise<Booking[] | null> {
    try {
      const response = await httpBase.get<Booking[]>(
        this.BOOKING_ENDPOINTS.GET_BOOKINGS,
        {},
        {
          onSuccess: (data) => {
            console.log(`✅ Loaded ${data.length} user bookings`);
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to load user bookings:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Update booking status
   */
  async updateBooking(
    bookingId: string,
    updates: { status?: string; notes?: string },
    callbacks?: ApiCallbacks<Booking>
  ): Promise<Booking | null> {
    try {
      const response = await httpBase.put<Booking>(
        `${this.BOOKING_ENDPOINTS.UPDATE_BOOKING}/${bookingId}`,
        updates,
        {},
        {
          onSuccess: (data) => {
            console.log(`✅ Booking ${bookingId} updated successfully`);
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to update booking:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Cancel a booking
   */
  async cancelBooking(
    bookingId: string,
    callbacks?: ApiCallbacks<{ message: string }>
  ): Promise<boolean> {
    try {
      await httpBase.delete(
        `${this.BOOKING_ENDPOINTS.CANCEL_BOOKING}/${bookingId}`,
        {},
        {
          onSuccess: (data) => {
            console.log(`✅ Booking ${bookingId} cancelled`);
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to cancel booking:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Group slots by date
   */
  groupSlotsByDate(slots: TimeSlot[]): Record<string, TimeSlot[]> {
    return slots.reduce((groups, slot) => {
      const date = slot.date;
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(slot);
      return groups;
    }, {} as Record<string, TimeSlot[]>);
  }

  /**
   * Format date for display
   */
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  /**
   * Format time for display
   */
  formatTime(timeString: string): string {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  }

  /**
   * Get service type display name
   */
  getServiceTypeDisplayName(serviceType: ServiceType): string {
    const displayNames: Record<ServiceType, string> = {
      consultation: 'Consultation',
      support: 'Support',
      demo: 'Demo',
      other: 'Other',
    };
    return displayNames[serviceType] || serviceType;
  }

  /**
   * Validate booking form data
   */
  validateBookingData(data: Partial<BookingRequest>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length < 2) {
      errors.push('Name must be at least 2 characters long');
    }

    if (!data.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push('Please enter a valid email address');
    }

    if (!data.phone || !/^\+?[\d\s\-\(\)]{10,15}$/.test(data.phone.replace(/\s/g, ''))) {
      errors.push('Please enter a valid phone number');
    }

    if (!data.service_type) {
      errors.push('Please select a service type');
    }

    if (!data.date) {
      errors.push('Please select a date');
    }

    if (!data.time) {
      errors.push('Please select a time');
    }

    if (!data.session_id) {
      errors.push('Session ID is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Check if a slot is in the past
   */
  isSlotInPast(slot: TimeSlot): boolean {
    const slotDateTime = new Date(slot.datetime);
    return slotDateTime < new Date();
  }

  /**
   * Get next available slot
   */
  getNextAvailableSlot(slots: TimeSlot[]): TimeSlot | null {
    const availableSlots = slots
      .filter(slot => slot.available && !this.isSlotInPast(slot))
      .sort((a, b) => new Date(a.datetime).getTime() - new Date(b.datetime).getTime());

    return availableSlots.length > 0 ? availableSlots[0] : null;
  }
}

// Export singleton instance
export const bookingService = new BookingService();
export default bookingService;
