@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Customizable accent color - can be overridden */
  --color-accent-main: #2563EB;
  --color-accent-light: #3B82F6;
  --color-accent-dark: #1D4ED8;

  /* Font settings for optimal readability */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Base styles following the design system */
body {
  @apply font-secondary text-text-primary bg-bg-primary;
  margin: 0;
  min-height: 100vh;
  line-height: 1.6;
}

/* Typography hierarchy */
h1, h2, h3, h4, h5, h6 {
  @apply font-primary font-semibold text-text-primary;
  line-height: 1.3;
}

h1 { @apply text-3xl lg:text-4xl; }
h2 { @apply text-2xl lg:text-3xl; }
h3 { @apply text-xl lg:text-2xl; }
h4 { @apply text-lg lg:text-xl; }
h5 { @apply text-base lg:text-lg; }
h6 { @apply text-sm lg:text-base; }

/* Links */
a {
  @apply text-accent-main hover:text-accent-dark transition-colors duration-200;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Focus styles for accessibility */
*:focus {
  @apply outline-none ring-2 ring-accent-main ring-opacity-50;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-bg-secondary;
}

::-webkit-scrollbar-thumb {
  @apply bg-border rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-text-light;
}
