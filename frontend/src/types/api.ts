// API Response Types based on backend models

// Common types
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

// User types
export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  is_active: boolean;
  created_at: string;
  last_login?: string;
  preferences: Record<string, any>;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  phone: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  message: string;
}

// Chat types
export interface ChatMessage {
  content: string;
  message_type: 'user' | 'assistant';
  timestamp: string;
  analytics?: Record<string, any>;
}

export interface ChatRequest {
  message: string;
  session_id?: string;
  user_id?: string;
}

export interface ChatResponse {
  response: string;
  session_id: string;
  user_message_id: string;
  assistant_message_id: string;
  response_time_ms: number;
  user_analytics?: Record<string, any>;
  assistant_analytics?: Record<string, any>;
}

export interface ConversationHistory {
  session_id: string;
  messages: ChatMessage[];
  total_messages: number;
}

// Booking types
export type ServiceType = 'consultation' | 'support' | 'demo' | 'other';
export type BookingStatus = 'confirmed' | 'cancelled' | 'completed' | 'no_show';

export interface TimeSlot {
  id: string;
  date: string;
  time: string;
  datetime: string;
  available: boolean;
  max_bookings: number;
  current_bookings: number;
  available_spots: number;
}

export interface BookingRequest {
  name: string;
  email: string;
  phone: string;
  service_type: ServiceType;
  date: string;
  time: string;
  notes?: string;
  session_id: string;
}

export interface Booking {
  id: string;
  booking_id: string;
  name: string;
  email: string;
  phone: string;
  service_type: ServiceType;
  date: string;
  time: string;
  notes?: string;
  status: BookingStatus;
  created_at: string;
}

// Dashboard types
export interface DashboardStats {
  total_users: number;
  total_messages: number;
  total_bookings: number;
  active_sessions: number;
  today_bookings: number;
  today_messages: number;
}

export interface UserAnalytics {
  user_id: string;
  analytics: Record<string, any>;
}

// Health check
export interface HealthResponse {
  status: string;
  timestamp: string;
  version: string;
  database_connected: boolean;
}
