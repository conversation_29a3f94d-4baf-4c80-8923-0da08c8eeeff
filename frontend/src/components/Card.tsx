/**
 * Card component following the monochrome design system
 */
import React from 'react';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  hover?: boolean;
  children: React.ReactNode;
}

const Card: React.FC<CardProps> = ({
  variant = 'default',
  padding = 'md',
  hover = false,
  className = '',
  children,
  ...props
}) => {
  // Base classes
  const baseClasses = [
    'bg-bg-primary',
    'rounded-card',
    'transition-all duration-200',
  ];

  // Variant classes
  const variantClasses = {
    default: ['border border-border'],
    elevated: ['shadow-card'],
    outlined: ['border-2 border-border'],
  };

  // Padding classes
  const paddingClasses = {
    none: [],
    sm: ['p-4'],
    md: ['p-6'],
    lg: ['p-8'],
  };

  // Hover classes
  const hoverClasses = hover ? [
    'hover:shadow-card-hover',
    'hover:border-accent-light',
    'cursor-pointer',
  ] : [];

  // Combine all classes
  const allClasses = [
    ...baseClasses,
    ...variantClasses[variant],
    ...paddingClasses[padding],
    ...hoverClasses,
    className,
  ].join(' ');

  return (
    <div className={allClasses} {...props}>
      {children}
    </div>
  );
};

// Card Header component
export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  subtitle?: string;
  action?: React.ReactNode;
  children?: React.ReactNode;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  title,
  subtitle,
  action,
  className = '',
  children,
  ...props
}) => {
  const headerClasses = [
    'flex items-start justify-between',
    'mb-4',
    className,
  ].join(' ');

  return (
    <div className={headerClasses} {...props}>
      <div className="flex-1 min-w-0">
        {title && (
          <h3 className="text-lg font-primary font-semibold text-text-primary mb-1">
            {title}
          </h3>
        )}
        {subtitle && (
          <p className="text-sm text-text-secondary">
            {subtitle}
          </p>
        )}
        {children}
      </div>
      {action && (
        <div className="flex-shrink-0 ml-4">
          {action}
        </div>
      )}
    </div>
  );
};

// Card Content component
export interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export const CardContent: React.FC<CardContentProps> = ({
  className = '',
  children,
  ...props
}) => {
  const contentClasses = [
    'text-text-primary',
    className,
  ].join(' ');

  return (
    <div className={contentClasses} {...props}>
      {children}
    </div>
  );
};

// Card Footer component
export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  align?: 'left' | 'center' | 'right' | 'between';
}

export const CardFooter: React.FC<CardFooterProps> = ({
  align = 'right',
  className = '',
  children,
  ...props
}) => {
  const alignClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
    between: 'justify-between',
  };

  const footerClasses = [
    'flex items-center',
    alignClasses[align],
    'mt-6 pt-4',
    'border-t border-border',
    className,
  ].join(' ');

  return (
    <div className={footerClasses} {...props}>
      {children}
    </div>
  );
};

export default Card;
