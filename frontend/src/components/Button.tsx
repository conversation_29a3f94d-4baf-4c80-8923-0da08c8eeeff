/**
 * Button component following the monochrome design system
 */
import React from 'react';
import { LucideIcon } from 'lucide-react';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  icon: Icon,
  iconPosition = 'left',
  fullWidth = false,
  className = '',
  disabled,
  ...props
}) => {
  // Base classes following the design system
  const baseClasses = [
    'inline-flex items-center justify-center',
    'font-primary font-medium',
    'rounded-soft',
    'transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-accent-main focus:ring-opacity-50',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    'select-none',
  ];

  // Variant classes
  const variantClasses = {
    primary: [
      'bg-accent-main text-white',
      'hover:bg-accent-dark',
      'active:bg-accent-dark',
      'shadow-sm hover:shadow-card',
    ],
    secondary: [
      'bg-bg-secondary text-text-primary',
      'hover:bg-bg-tertiary',
      'active:bg-bg-tertiary',
      'border border-border',
    ],
    outline: [
      'bg-transparent text-text-primary',
      'border border-border',
      'hover:bg-bg-secondary',
      'active:bg-bg-tertiary',
    ],
    ghost: [
      'bg-transparent text-text-primary',
      'hover:bg-bg-secondary',
      'active:bg-bg-tertiary',
    ],
    danger: [
      'bg-error text-white',
      'hover:bg-red-700',
      'active:bg-red-700',
      'shadow-sm hover:shadow-card',
    ],
  };

  // Size classes
  const sizeClasses = {
    sm: ['px-3 py-1.5 text-sm', 'gap-1.5'],
    md: ['px-4 py-2 text-base', 'gap-2'],
    lg: ['px-6 py-3 text-lg', 'gap-2.5'],
  };

  // Width classes
  const widthClasses = fullWidth ? ['w-full'] : [];

  // Combine all classes
  const allClasses = [
    ...baseClasses,
    ...variantClasses[variant],
    ...sizeClasses[size],
    ...widthClasses,
    className,
  ].join(' ');

  // Loading spinner component
  const LoadingSpinner = () => (
    <svg
      className="animate-spin h-4 w-4"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );

  return (
    <button
      className={allClasses}
      disabled={disabled || loading}
      {...props}
    >
      {loading && <LoadingSpinner />}
      {!loading && Icon && iconPosition === 'left' && <Icon size={16} />}
      {children && (
        <span className={loading ? 'opacity-0' : ''}>{children}</span>
      )}
      {!loading && Icon && iconPosition === 'right' && <Icon size={16} />}
    </button>
  );
};

export default Button;
