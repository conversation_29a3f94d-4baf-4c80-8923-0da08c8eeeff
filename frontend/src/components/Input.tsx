/**
 * Input component following the monochrome design system
 */
import React, { forwardRef } from 'react';
import { LucideIcon } from 'lucide-react';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  variant?: 'default' | 'filled';
}

const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  icon: Icon,
  iconPosition = 'left',
  fullWidth = false,
  variant = 'default',
  className = '',
  id,
  ...props
}, ref) => {
  // Generate unique ID if not provided
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

  // Base input classes
  const baseInputClasses = [
    'w-full',
    'px-3 py-2',
    'font-secondary text-text-primary',
    'bg-bg-primary',
    'border border-border',
    'rounded-soft',
    'transition-all duration-200',
    'placeholder:text-text-light',
    'focus:outline-none focus:ring-2 focus:ring-accent-main focus:ring-opacity-50 focus:border-accent-main',
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-bg-secondary',
  ];

  // Variant classes
  const variantClasses = {
    default: [],
    filled: ['bg-bg-secondary border-transparent'],
  };

  // Error state classes
  const errorClasses = error ? [
    'border-error',
    'focus:ring-error focus:ring-opacity-50 focus:border-error',
  ] : [];

  // Icon padding classes
  const iconPaddingClasses = Icon ? (
    iconPosition === 'left' ? ['pl-10'] : ['pr-10']
  ) : [];

  // Combine input classes
  const inputClasses = [
    ...baseInputClasses,
    ...variantClasses[variant],
    ...errorClasses,
    ...iconPaddingClasses,
    className,
  ].join(' ');

  // Container classes
  const containerClasses = [
    fullWidth ? 'w-full' : 'w-auto',
    'relative',
  ].join(' ');

  // Label classes
  const labelClasses = [
    'block',
    'text-sm font-medium text-text-primary',
    'mb-1.5',
    error ? 'text-error' : '',
  ].join(' ');

  // Helper text classes
  const helperTextClasses = [
    'mt-1.5',
    'text-sm',
    error ? 'text-error' : 'text-text-secondary',
  ].join(' ');

  // Icon classes
  const iconClasses = [
    'absolute top-1/2 transform -translate-y-1/2',
    'text-text-light',
    iconPosition === 'left' ? 'left-3' : 'right-3',
    'pointer-events-none',
  ].join(' ');

  return (
    <div className={containerClasses}>
      {label && (
        <label htmlFor={inputId} className={labelClasses}>
          {label}
          {props.required && <span className="text-error ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <input
          ref={ref}
          id={inputId}
          className={inputClasses}
          {...props}
        />
        
        {Icon && (
          <Icon size={16} className={iconClasses} />
        )}
      </div>
      
      {(error || helperText) && (
        <p className={helperTextClasses}>
          {error || helperText}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
