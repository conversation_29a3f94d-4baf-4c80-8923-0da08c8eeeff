/**
 * Loading component following the monochrome design system
 */
import React from 'react';

export interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'spinner' | 'dots' | 'pulse';
  text?: string;
  fullScreen?: boolean;
  className?: string;
}

const Loading: React.FC<LoadingProps> = ({
  size = 'md',
  variant = 'spinner',
  text,
  fullScreen = false,
  className = '',
}) => {
  // Size classes
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
  };

  // Spinner component
  const Spinner = () => (
    <svg
      className={`animate-spin ${sizeClasses[size]} text-accent-main`}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );

  // Dots component
  const Dots = () => {
    const dotSize = {
      sm: 'w-1 h-1',
      md: 'w-2 h-2',
      lg: 'w-3 h-3',
    };

    return (
      <div className="flex space-x-1">
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className={`
              ${dotSize[size]}
              bg-accent-main
              rounded-full
              animate-pulse-soft
            `}
            style={{
              animationDelay: `${i * 0.2}s`,
            }}
          />
        ))}
      </div>
    );
  };

  // Pulse component
  const Pulse = () => (
    <div
      className={`
        ${sizeClasses[size]}
        bg-accent-main
        rounded-full
        animate-pulse-soft
      `}
    />
  );

  // Render loading indicator based on variant
  const renderLoadingIndicator = () => {
    switch (variant) {
      case 'dots':
        return <Dots />;
      case 'pulse':
        return <Pulse />;
      case 'spinner':
      default:
        return <Spinner />;
    }
  };

  // Container classes
  const containerClasses = [
    'flex flex-col items-center justify-center',
    fullScreen ? 'fixed inset-0 bg-bg-primary z-50' : '',
    className,
  ].join(' ');

  return (
    <div className={containerClasses}>
      {renderLoadingIndicator()}
      {text && (
        <p className="mt-3 text-sm text-text-secondary font-secondary">
          {text}
        </p>
      )}
    </div>
  );
};

// Skeleton component for content loading
export interface SkeletonProps {
  width?: string | number;
  height?: string | number;
  className?: string;
  variant?: 'text' | 'rectangular' | 'circular';
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = '1rem',
  className = '',
  variant = 'text',
}) => {
  const baseClasses = [
    'bg-bg-secondary',
    'animate-pulse-soft',
  ];

  const variantClasses = {
    text: 'rounded',
    rectangular: 'rounded-soft',
    circular: 'rounded-full',
  };

  const allClasses = [
    ...baseClasses,
    variantClasses[variant],
    className,
  ].join(' ');

  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  };

  return <div className={allClasses} style={style} />;
};

// Loading overlay component
export interface LoadingOverlayProps {
  isLoading: boolean;
  text?: string;
  children: React.ReactNode;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  text,
  children,
}) => {
  return (
    <div className="relative">
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-bg-primary bg-opacity-75 flex items-center justify-center z-10">
          <Loading text={text} />
        </div>
      )}
    </div>
  );
};

export default Loading;
