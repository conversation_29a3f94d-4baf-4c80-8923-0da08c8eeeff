#!/bin/bash

# Function to run backend
run_backend() {
    echo "🚀 Starting backend..."
    cd backend && uv run fastapi run main.py
}

# Function to run frontend
run_frontend() {
    echo "🚀 Starting frontend..."
    cd frontend && npm run build && npx serve -l 3000 build
}

# Check if argument is provided
if [ "$1" = "backend" ]; then
    run_backend
elif [ "$1" = "frontend" ]; then
    run_frontend
elif [ "$1" = "both" ]; then
    # Run both in parallel
    echo "🚀 Starting both backend and frontend..."
    run_backend &
    BACKEND_PID=$!
    run_frontend &
    FRONTEND_PID=$!

    # Wait for both processes
    wait $BACKEND_PID $FRONTEND_PID
else
    echo "Usage: $0 [backend|frontend|both]"
    echo "  backend  - Run only the backend server"
    echo "  frontend - Run only the frontend server"
    echo "  both     - Run both servers in parallel"
    exit 1
fi