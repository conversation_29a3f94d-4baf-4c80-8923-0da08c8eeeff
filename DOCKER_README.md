# Docker Setup for Chat System

This document explains how to run the Chat System using Docker and Docker Compose.

## Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose installed
- MongoDB instance running (local or cloud)
- Environment variables configured

## Quick Start

1. **Clone the repository and navigate to the root directory**

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env file with your MongoDB URI and other configurations
   ```

3. **Build and run the services**
   ```bash
   docker-compose up --build
   ```

4. **Access the application**
   - Frontend: http://localhost:8201
   - Backend API: http://localhost:8020 (primary), http://localhost:8021, http://localhost:8022 (replicas)
   - API Documentation: http://localhost:8020/docs

## Environment Variables

### Required
- `MONGO_URI`: MongoDB connection string (e.g., `mongodb://localhost:27017/`)
- `DATABASE_NAME`: Database name (default: `chatbot_system`)

### Optional
- `OPENAI_API_KEY`: OpenAI API key for AI features
- `ANTHROPIC_API_KEY`: Anthropic API key for AI features

## Docker Commands

### Build and start services
```bash
docker-compose up --build
```

### Start services in background
```bash
docker-compose up -d
```

### Stop services
```bash
docker-compose down
```

### View logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend
```

### Rebuild specific service
```bash
docker-compose build backend
docker-compose build frontend
```

### Scale services (if needed)
```bash
docker-compose up --scale backend=2
```

## Service Details

### Backend Service
- **Ports**: 8020-8025 (3 replicas)
- **Internal Port**: 8000
- **Health Check**: `/health` endpoint
- **Technology**: FastAPI with Python
- **Database**: MongoDB (external)
- **Replicas**: 3 instances for load distribution

### Frontend Service
- **Port**: 8201 (host) → 3000 (container)
- **Technology**: React with TypeScript + Vite dev server
- **Build**: Simple Node.js container with Vite
- **API Connection**: Points to backend at localhost:8020

## Development vs Production

### Development
For development, you can mount volumes to enable hot reloading:

```yaml
# Add to docker-compose.yml under backend service
volumes:
  - ./backend:/app
  - /app/.venv  # Exclude virtual environment
```

### Production
The current setup is optimized for production with:
- Multi-stage builds for smaller images
- Non-root users for security
- Health checks for reliability
- Nginx for serving static files
- Gzip compression enabled

## Troubleshooting

### Common Issues

1. **MongoDB Connection Failed**
   - Ensure MongoDB is running and accessible
   - Check MONGO_URI in .env file
   - Verify network connectivity

2. **Port Already in Use**
   ```bash
   # Change ports in docker-compose.yml
   ports:
     - "8030-8035:8000"  # Backend replicas
     - "8202:3000"       # Frontend
   ```

3. **Build Failures**
   ```bash
   # Clean build
   docker-compose down
   docker system prune -f
   docker-compose build --no-cache
   ```

4. **Permission Issues**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   ```

### Logs and Debugging

```bash
# Check service status
docker-compose ps

# Inspect specific container
docker inspect chat-system-backend

# Execute commands in running container
docker-compose exec backend bash
docker-compose exec frontend sh
```

## Security Considerations

- Services run as non-root users
- Security headers configured in Nginx
- Environment variables for sensitive data
- Network isolation with Docker networks
- Health checks for service monitoring

## Monitoring

Both services include health checks:
- Backend: `http://localhost:8020/health` (and 8021, 8022 for replicas)
- Frontend: `http://localhost:8201` (Vite dev server)

You can integrate these with monitoring tools like:
- Prometheus
- Grafana
- Docker Swarm
- Kubernetes

## Scaling

To scale the application:

1. **Horizontal Scaling**
   ```bash
   docker-compose up --scale backend=3
   ```

2. **Load Balancer**
   Add a load balancer service (nginx, traefik, etc.)

3. **Database Scaling**
   Consider MongoDB replica sets or sharding

## Backup and Recovery

### Database Backup
```bash
# Backup MongoDB (if running locally)
docker exec -it mongodb mongodump --out /backup

# Restore
docker exec -it mongodb mongorestore /backup
```

### Application Data
- Backend: Stateless (no persistent data)
- Frontend: Static files (can be rebuilt)
- Database: External MongoDB (handle separately)
