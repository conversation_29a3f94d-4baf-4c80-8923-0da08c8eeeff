[project]
name = "backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
        "langchain>=0.1.0",
        "langchain-google-genai>=1.0.0",
        "langchain-openai>=0.1.0",
        "langchain-qdrant>=0.1.0",
        "langgraph>=0.1.0",
        "python-dotenv>=1.0.0",
        "qdrant-client>=1.7.0",
        "uvicorn[standard]>=0.24.0",
        "textblob>=0.17.0",
        "langdetect>=1.0.9",
        "argon2-cffi>=23.1.0",
        "python-jose[cryptography]>=3.3.0",
        "python-multipart>=0.0.6",
        "pydantic>=2.5.0",
        "pymongo>=4.6.0",
        "fastapi[standard]>=0.115.14",
]
