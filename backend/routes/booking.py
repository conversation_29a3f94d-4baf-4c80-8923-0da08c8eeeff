"""
Booking routes for appointment management
"""
from datetime import datetime, date
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel, EmailStr

from models.booking import (
    BookingService, BookingCreate, BookingResponse, BookingUpdate,
    TimeSlotInDB, ServiceType, BookingStatus
)
from config.database import get_collection, COLLECTIONS
from routes.auth import get_current_user

router = APIRouter()

# Request/Response models
class BookingRequest(BaseModel):
    name: str
    email: EmailStr
    phone: str
    service_type: ServiceType
    date: str  # YYYY-MM-DD format
    time: str  # HH:MM format
    notes: Optional[str] = None
    session_id: str

class TimeSlotResponse(BaseModel):
    id: str
    date: str
    time: str
    datetime: datetime
    available: bool
    max_bookings: int
    current_bookings: int
    available_spots: int

class BookingListResponse(BaseModel):
    bookings: List[BookingResponse]
    total: int

def get_booking_service():
    """Get booking service instance"""
    return BookingService(
        get_collection(COLLECTIONS['bookings']),
        get_collection(COLLECTIONS['time_slots'])
    )

# Routes
@router.get("/slots", response_model=List[TimeSlotResponse])
async def get_available_slots(days_ahead: int = Query(14, ge=1, le=30)):
    """Get available appointment slots"""
    try:
        booking_service = get_booking_service()
        slots = booking_service.get_available_slots(days_ahead)
        
        return [
            TimeSlotResponse(
                id=str(slot.id),
                date=slot.date,
                time=slot.time,
                datetime=slot.datetime,
                available=slot.available,
                max_bookings=slot.max_bookings,
                current_bookings=slot.current_bookings,
                available_spots=slot.max_bookings - slot.current_bookings
            )
            for slot in slots
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve available slots: {str(e)}"
        )

@router.post("/book", response_model=BookingResponse)
async def create_booking(booking_request: BookingRequest):
    """Create a new booking"""
    try:
        booking_service = get_booking_service()
        
        # Create booking
        booking_data = BookingCreate(
            name=booking_request.name,
            email=booking_request.email,
            phone=booking_request.phone,
            service_type=booking_request.service_type,
            date=booking_request.date,
            time=booking_request.time,
            notes=booking_request.notes,
            session_id=booking_request.session_id
        )
        
        booking = booking_service.create_booking(booking_data)
        
        return BookingResponse(
            id=str(booking.id),
            booking_id=booking.booking_id,
            name=booking.name,
            email=booking.email,
            phone=booking.phone,
            service_type=booking.service_type,
            date=booking.date,
            time=booking.time,
            notes=booking.notes,
            status=booking.status,
            created_at=booking.created_at
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Booking creation failed: {str(e)}"
        )

@router.get("/booking/{booking_id}", response_model=BookingResponse)
async def get_booking(booking_id: str):
    """Get booking by ID"""
    try:
        booking_service = get_booking_service()
        booking = booking_service.get_booking_by_id(booking_id)
        
        if not booking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Booking {booking_id} not found"
            )
        
        return BookingResponse(
            id=str(booking.id),
            booking_id=booking.booking_id,
            name=booking.name,
            email=booking.email,
            phone=booking.phone,
            service_type=booking.service_type,
            date=booking.date,
            time=booking.time,
            notes=booking.notes,
            status=booking.status,
            created_at=booking.created_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve booking: {str(e)}"
        )

@router.get("/bookings/email/{email}", response_model=BookingListResponse)
async def get_bookings_by_email(email: str):
    """Get bookings by email address"""
    try:
        booking_service = get_booking_service()
        bookings = booking_service.get_bookings_by_email(email)
        
        booking_responses = [
            BookingResponse(
                id=str(booking.id),
                booking_id=booking.booking_id,
                name=booking.name,
                email=booking.email,
                phone=booking.phone,
                service_type=booking.service_type,
                date=booking.date,
                time=booking.time,
                notes=booking.notes,
                status=booking.status,
                created_at=booking.created_at
            )
            for booking in bookings
        ]
        
        return BookingListResponse(
            bookings=booking_responses,
            total=len(booking_responses)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve bookings: {str(e)}"
        )

@router.get("/my-bookings", response_model=BookingListResponse)
async def get_my_bookings(current_user = Depends(get_current_user)):
    """Get current user's bookings"""
    try:
        booking_service = get_booking_service()
        bookings = booking_service.get_user_bookings(str(current_user.id))
        
        booking_responses = [
            BookingResponse(
                id=str(booking.id),
                booking_id=booking.booking_id,
                name=booking.name,
                email=booking.email,
                phone=booking.phone,
                service_type=booking.service_type,
                date=booking.date,
                time=booking.time,
                notes=booking.notes,
                status=booking.status,
                created_at=booking.created_at
            )
            for booking in bookings
        ]
        
        return BookingListResponse(
            bookings=booking_responses,
            total=len(booking_responses)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve user bookings: {str(e)}"
        )

@router.put("/booking/{booking_id}/status")
async def update_booking_status(
    booking_id: str,
    status_update: BookingUpdate,
    current_user = Depends(get_current_user)
):
    """Update booking status (admin only for now)"""
    try:
        booking_service = get_booking_service()
        
        if status_update.status:
            updated_booking = booking_service.update_booking_status(
                booking_id, 
                status_update.status
            )
            
            if not updated_booking:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Booking {booking_id} not found"
                )
            
            return {
                "message": f"Booking {booking_id} status updated to {status_update.status}",
                "booking_id": booking_id,
                "new_status": status_update.status
            }
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No status provided for update"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update booking status: {str(e)}"
        )

@router.delete("/booking/{booking_id}")
async def cancel_booking(booking_id: str):
    """Cancel a booking"""
    try:
        booking_service = get_booking_service()
        success = booking_service.cancel_booking(booking_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Booking {booking_id} not found or cannot be cancelled"
            )
        
        return {
            "message": f"Booking {booking_id} has been cancelled successfully",
            "booking_id": booking_id,
            "status": "cancelled"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel booking: {str(e)}"
        )

@router.get("/daily/{date}")
async def get_daily_bookings(
    date: str,
    current_user = Depends(get_current_user)
):
    """Get all bookings for a specific date (admin view)"""
    try:
        booking_service = get_booking_service()
        bookings = booking_service.get_daily_bookings(date)
        
        booking_responses = [
            BookingResponse(
                id=str(booking.id),
                booking_id=booking.booking_id,
                name=booking.name,
                email=booking.email,
                phone=booking.phone,
                service_type=booking.service_type,
                date=booking.date,
                time=booking.time,
                notes=booking.notes,
                status=booking.status,
                created_at=booking.created_at
            )
            for booking in bookings
        ]
        
        return {
            "date": date,
            "bookings": booking_responses,
            "total": len(booking_responses)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve daily bookings: {str(e)}"
        )
