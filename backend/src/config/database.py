"""
Database configuration and connection setup for MongoDB using AsyncMongoClient
"""
import os
from pymongo import AsyncMongoClient
from pymongo.errors import ConnectionFailure
from dotenv import load_dotenv
import logging
import asyncio

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseConfig:
    """Async Database configuration and connection management"""

    def __init__(self):
        self.mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
        self.database_name = os.getenv("DATABASE_NAME", "chatbot_system")
        self.client: AsyncMongoClient = None
        self.db = None

    async def connect(self):
        """Establish connection to MongoDB"""
        try:
            self.client = AsyncMongoClient(self.mongo_uri)
            # Test the connection
            await self.client.admin.command('ping')
            self.db = self.client[self.database_name]
            logger.info(f"Successfully connected to MongoDB: {self.database_name}")
            return True
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB: {e}")
            return False

    async def get_database(self):
        """Get database instance"""
        if self.db is None:
            await self.connect()
        return self.db

    def get_database_sync(self):
        """Get database instance synchronously (assumes connection is already established)"""
        return self.db

    async def get_collection(self, collection_name: str):
        """Get specific collection"""
        db = await self.get_database()
        return db[collection_name]

    def close_connection(self):
        """Close database connection"""
        if self.client:
            self.client.close()
            logger.info("Database connection closed")

# Global database instance
db_config = DatabaseConfig()

async def get_db():
    """Get database instance"""
    return await db_config.get_database()

async def get_collection(collection_name):
    """Get specific collection"""
    return await db_config.get_collection(collection_name)

# Collection names
COLLECTIONS = {
    'users': 'users',
    'messages': 'messages', 
    'bookings': 'bookings',
    'time_slots': 'time_slots',
    'analytics': 'analytics'
}

async def initialize_collections():
    """Initialize collections with indexes and constraints"""
    db = await get_db()

    # Users collection indexes
    users_collection = db[COLLECTIONS['users']]
    await users_collection.create_index("email", unique=True)
    await users_collection.create_index("phone", unique=True)

    # Messages collection indexes
    messages_collection = db[COLLECTIONS['messages']]
    await messages_collection.create_index([("user_id", 1), ("timestamp", -1)])
    await messages_collection.create_index("session_id")

    # Bookings collection indexes
    bookings_collection = db[COLLECTIONS['bookings']]
    await bookings_collection.create_index([("user_id", 1), ("booking_date", -1)])
    await bookings_collection.create_index([("date", 1), ("time", 1)], unique=True)

    # Time slots collection indexes
    time_slots_collection = db[COLLECTIONS['time_slots']]
    await time_slots_collection.create_index([("date", 1), ("time", 1)], unique=True)

    # Analytics collection indexes
    analytics_collection = db[COLLECTIONS['analytics']]
    await analytics_collection.create_index([("date", 1), ("metric_type", 1)])

    logger.info("Collections and indexes initialized successfully")

async def create_sample_time_slots():
    """Create sample time slots for booking system"""
    from datetime import datetime, timedelta

    time_slots_collection = await get_collection(COLLECTIONS['time_slots'])

    # Clear existing slots
    await time_slots_collection.delete_many({})

    # Create slots for next 30 days
    base_date = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
    slots = []

    for i in range(30):  # Next 30 days
        date = base_date + timedelta(days=i)
        # Skip weekends
        if date.weekday() < 5:  # Monday=0, Friday=4
            for hour in [9, 11, 14, 16]:  # 9AM, 11AM, 2PM, 4PM
                slot_datetime = date.replace(hour=hour)
                slots.append({
                    "date": slot_datetime.strftime("%Y-%m-%d"),
                    "time": slot_datetime.strftime("%H:%M"),
                    "datetime": slot_datetime,
                    "available": True,
                    "max_bookings": 5,  # Allow multiple bookings per slot
                    "current_bookings": 0,
                    "created_at": datetime.utcnow()
                })

    if slots:
        await time_slots_collection.insert_many(slots)
        logger.info(f"Created {len(slots)} time slots")

async def main():
    """Main function for testing database setup"""
    if await db_config.connect():
        await initialize_collections()
        await create_sample_time_slots()
        print("Database setup completed successfully!")
    else:
        print("Failed to setup database")

if __name__ == "__main__":
    asyncio.run(main())
