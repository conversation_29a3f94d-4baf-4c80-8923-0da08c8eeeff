"""
Enhanced chat service with sentiment analysis and language detection
"""
import os
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv

# LangChain and LangGraph imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import HumanMessage

# Qdrant imports for search functionality
from qdrant_client import QdrantClient
from langchain_openai import OpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore, RetrievalMode
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.prompts import ChatPromptTemplate

# Local imports
from src.models.message import MessageService, MessageCreate, MessageType
from src.models.booking import BookingService
from src.config.database import COLLECTIONS

# Load environment variables
load_dotenv()

class EnhancedChatService:
    """Enhanced chat service with analytics and database integration"""
    
    def __init__(self):
        self.setup_llm()
        self.setup_search()
        self.setup_database_services()
        self.setup_agent()
    
    def setup_llm(self):
        """Setup Gemini model"""
        self.model = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
    
    def setup_search(self):
        """Setup Qdrant search functionality"""
        # Setup embeddings
        self.embeddings = OpenAIEmbeddings(
            model="text-embedding-3-large",
            api_key=os.getenv("OPENAI_API_KEY"),
            dimensions=1536
        )
        
        # Initialize Qdrant client
        self.qdrant_client = QdrantClient(
            host=os.getenv("QDRANT_HOST", "*************"),
            port=int(os.getenv("QDRANT_PORT", "6333")),
        )
        
        # Setup vector store
        self.vector_store = QdrantVectorStore(
            client=self.qdrant_client,
            collection_name=os.getenv("QDRANT_COLLECTION", "langsmit_test"),
            embedding=self.embeddings,
            retrieval_mode=RetrievalMode.DENSE,
            content_payload_key="page_content",
            metadata_payload_key="metadata"
        )
        
        # Setup retrieval chain
        self.retriever = self.vector_store.as_retriever(
            search_type="mmr", 
            search_kwargs={"k": 5}
        )
        
        # Create search prompt
        system_prompt = (
            "You are an assistant for question-answering tasks. "
            "Use the following pieces of retrieved context to answer "
            "the question. If you don't know the answer, say that you "
            "don't know. Use three sentences maximum and keep the "
            "answer concise."
            "\n\n"
            "{context}"
        )
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            ("human", "{input}"),
        ])
        
        question_answer_chain = create_stuff_documents_chain(self.model, prompt)
        self.qa_chain = create_retrieval_chain(self.retriever, question_answer_chain)
    
    def setup_database_services(self):
        """Setup database services"""
        from src.config.database import db_config
        # Use synchronous database access
        db = db_config.get_database_sync()
        self.message_service = MessageService(db[COLLECTIONS['messages']])
        self.booking_service = BookingService(
            db[COLLECTIONS['bookings']],
            db[COLLECTIONS['time_slots']]
        )
    
    def setup_agent(self):
        """Setup LangGraph agent with tools"""
        from src.tools.enhanced_tools import get_enhanced_tools
        
        # Get enhanced tools with database integration
        self.tools = get_enhanced_tools(self.qa_chain, self.booking_service)
        
        # Create system prompt
        self.system_prompt = """You are a professional AI assistant for a customer service center. You MUST use the available tools to help customers.

The message can be in Nepali (नेपाली), Romanized Nepali, English, or a mix of both.

AVAILABLE TOOLS:
1. **search_database**: Use this tool for ANY question or queries about products, services, apps, or information
2. **get_current_date**: Use this tool to get current date and time (MANDATORY for booking processes)
3. **get_available_slots**: Use this tool to show available appointment slots
4. **book_appointment**: Use this tool to schedule appointments

CRITICAL RULES:
- MANDATORY: Use search_database tool for ANY question that needs information (apps, troubleshooting, products, services, "how to", etc.)
- NEVER answer informational questions without using search_database tool first
- Questions like "my app is not working", "how to download", "what is", "I need help with" → MUST use search_database
- For booking: ALWAYS get current date first, then show available slots, then book
- Use get_current_date tool when user asks about dates or for booking processes
- Use get_available_slots when user asks "any available slot", "when can I book", etc.
- Use relevant keywords from user's question for search
- Handle Nepali, Romanized Nepali, and English input
- Respond in clear English or Romanized Nepali
- Do NOT show tool calls in response - only show the final clean answer
- Only simple greetings like "Hello" don't need tools

BOOKING PROCESS:
1. Search for service/course info if needed
2. Get current date (MANDATORY)
3. Show available slots
4. Collect customer details
5. Book appointment

EXAMPLES OF MANDATORY TOOL USAGE:
- "my app is not working" → search_database("app not working troubleshooting")
- "any available slot" → get_available_slots()
- "what's today's date" → get_current_date()
- "book Loksewa" → search_database("Loksewa") → get_current_date() → get_available_slots()

IMPORTANT:
- ALWAYS search first, then provide clean response without showing tool usage
- Response should be helpful and in user's preferred language style
"""
        
        # Create memory for conversation persistence
        self.checkpointer = MemorySaver()
        
        # Create the agentic chatbot
        self.agent = create_react_agent(
            model=self.model,
            tools=self.tools,
            prompt=self.system_prompt,
            checkpointer=self.checkpointer
        )
    
    async def process_message(
        self, 
        content: str, 
        session_id: str, 
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Process user message with analytics"""
        start_time = datetime.now()
        
        try:
            # Store user message with analytics
            user_message =await self.message_service.create_message(
                MessageCreate(
                    content=content,
                    message_type=MessageType.USER,
                    session_id=session_id,
                    user_id=user_id
                )
            )
            
            # Process with agent
            config = {"configurable": {"thread_id": session_id}}
            response = self.agent.invoke(
                {"messages": [HumanMessage(content=content)]},
                config=config
            )
            
            # Extract assistant response
            assistant_content = response["messages"][-1].content
            
            # Calculate response time
            end_time = datetime.now()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Store assistant message with analytics
            assistant_message =await self.message_service.create_message(
                MessageCreate(
                    content=assistant_content,
                    message_type=MessageType.ASSISTANT,
                    session_id=session_id,
                    user_id=user_id
                ),
                response_time_ms=response_time_ms
            )
            
            return {
                "response": assistant_content,
                "session_id": session_id,
                "user_message_id": str(user_message.id),
                "assistant_message_id": str(assistant_message.id),
                "response_time_ms": response_time_ms,
                "user_analytics": user_message.analytics.dict() if user_message.analytics else None,
                "assistant_analytics": assistant_message.analytics.dict() if assistant_message.analytics else None
            }
            
        except Exception as e:
            # Store error message
            error_message = await self.message_service.create_message(
                MessageCreate(
                    content=f"Error processing message: {str(e)}",
                    message_type=MessageType.SYSTEM,
                    session_id=session_id,
                    user_id=user_id
                )
            )
            
            return {
                "response": "I apologize, but I encountered an error processing your message. Please try again.",
                "session_id": session_id,
                "error": str(e),
                "error_message_id": str(error_message.id)
            }
    
    def get_conversation_history(self, session_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get conversation history with analytics"""
        messages = self.message_service.get_messages_by_session(session_id, limit)
        
        return [
            {
                "id": str(msg.id),
                "content": msg.content,
                "type": msg.message_type,
                "timestamp": msg.timestamp.isoformat(),
                "analytics": msg.analytics.dict() if msg.analytics else None
            }
            for msg in messages
        ]
    
    def get_user_analytics(self, user_id: str) -> Dict[str, Any]:
        """Get analytics summary for a user"""
        messages = self.message_service.get_user_messages(user_id)
        
        if not messages:
            return {"total_messages": 0}
        
        # Calculate analytics
        total_messages = len(messages)
        user_messages = [msg for msg in messages if msg.message_type == MessageType.USER]
        
        if not user_messages:
            return {"total_messages": total_messages, "user_messages": 0}
        
        # Sentiment analysis
        sentiments = [msg.analytics.sentiment for msg in user_messages if msg.analytics]
        sentiment_scores = [msg.analytics.sentiment_score for msg in user_messages if msg.analytics]
        
        # Language analysis
        languages = [msg.analytics.language for msg in user_messages if msg.analytics]
        
        # Intent analysis
        booking_intents = sum(1 for msg in user_messages if msg.analytics and msg.analytics.contains_booking_intent)
        complaints = sum(1 for msg in user_messages if msg.analytics and msg.analytics.contains_complaint)
        
        return {
            "total_messages": total_messages,
            "user_messages": len(user_messages),
            "sentiment_distribution": {
                "positive": sentiments.count("positive"),
                "negative": sentiments.count("negative"),
                "neutral": sentiments.count("neutral")
            },
            "average_sentiment_score": sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0,
            "languages_used": list(set(languages)),
            "booking_intents": booking_intents,
            "complaints": complaints,
            "last_activity": messages[0].timestamp.isoformat() if messages else None
        }

# Global chat service instance
chat_service = None

def get_chat_service() -> EnhancedChatService:
    """Get or create chat service instance"""
    global chat_service
    if chat_service is None:
        chat_service = EnhancedChatService()
    return chat_service
