"""
Chat routes for enhanced chat functionality
"""
from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel

from src.services.chat_service import get_chat_service
from src.routes.auth import get_current_user

router = APIRouter()

# Request/Response models
class ChatMessage(BaseModel):
    content: str
    message_type: str
    timestamp: str
    analytics: Optional[Dict[str, Any]] = None

class ConversationHistoryResponse(BaseModel):
    session_id: str
    messages: List[Dict[str, Any]]
    total_messages: int

class UserAnalyticsResponse(BaseModel):
    user_id: str
    analytics: Dict[str, Any]

class SessionListResponse(BaseModel):
    sessions: List[Dict[str, str]]
    total: int

# Routes
@router.get("/sessions", response_model=SessionListResponse)
async def get_user_sessions(current_user = Depends(get_current_user)):
    """Get all chat sessions for the current user"""
    try:
        chat_service = get_chat_service()
        
        # Get user messages and extract unique session IDs
        messages = chat_service.message_service.get_user_messages(str(current_user.id), 1000)
        
        # Group by session and get session info
        sessions = {}
        for message in messages:
            session_id = message.session_id
            if session_id not in sessions:
                sessions[session_id] = {
                    "session_id": session_id,
                    "last_activity": message.timestamp.isoformat(),
                    "message_count": 0
                }
            sessions[session_id]["message_count"] += 1
            
            # Update last activity if this message is more recent
            if message.timestamp.isoformat() > sessions[session_id]["last_activity"]:
                sessions[session_id]["last_activity"] = message.timestamp.isoformat()
        
        session_list = list(sessions.values())
        session_list.sort(key=lambda x: x["last_activity"], reverse=True)
        
        return SessionListResponse(
            sessions=session_list,
            total=len(session_list)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve user sessions: {str(e)}"
        )

@router.get("/history/{session_id}", response_model=ConversationHistoryResponse)
async def get_session_history(session_id: str, limit: int = 50):
    """Get conversation history for a specific session"""
    try:
        chat_service = get_chat_service()
        history = chat_service.get_conversation_history(session_id, limit)
        
        return ConversationHistoryResponse(
            session_id=session_id,
            messages=history,
            total_messages=len(history)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve conversation history: {str(e)}"
        )

@router.get("/analytics/user", response_model=UserAnalyticsResponse)
async def get_current_user_analytics(current_user = Depends(get_current_user)):
    """Get analytics for the current user"""
    try:
        chat_service = get_chat_service()
        analytics = chat_service.get_user_analytics(str(current_user.id))
        
        return UserAnalyticsResponse(
            user_id=str(current_user.id),
            analytics=analytics
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve user analytics: {str(e)}"
        )

@router.get("/analytics/session/{session_id}")
async def get_session_analytics(session_id: str):
    """Get analytics for a specific session"""
    try:
        chat_service = get_chat_service()
        messages = chat_service.message_service.get_messages_by_session(session_id)
        
        if not messages:
            return {
                "session_id": session_id,
                "analytics": {"total_messages": 0}
            }
        
        # Calculate session analytics
        user_messages = [msg for msg in messages if msg.message_type == "user"]
        assistant_messages = [msg for msg in messages if msg.message_type == "assistant"]
        
        # Sentiment analysis
        sentiments = [msg.analytics.sentiment for msg in user_messages if msg.analytics]
        sentiment_scores = [msg.analytics.sentiment_score for msg in user_messages if msg.analytics]
        
        # Language analysis
        languages = [msg.analytics.language for msg in user_messages if msg.analytics]
        
        # Intent analysis
        booking_intents = sum(1 for msg in user_messages if msg.analytics and msg.analytics.contains_booking_intent)
        complaints = sum(1 for msg in user_messages if msg.analytics and msg.analytics.contains_complaint)
        
        # Response times
        response_times = [msg.analytics.response_time_ms for msg in assistant_messages if msg.analytics and msg.analytics.response_time_ms]
        
        analytics = {
            "session_id": session_id,
            "total_messages": len(messages),
            "user_messages": len(user_messages),
            "assistant_messages": len(assistant_messages),
            "sentiment_distribution": {
                "positive": sentiments.count("positive"),
                "negative": sentiments.count("negative"),
                "neutral": sentiments.count("neutral")
            },
            "average_sentiment_score": sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0,
            "languages_used": list(set(languages)),
            "booking_intents": booking_intents,
            "complaints": complaints,
            "average_response_time_ms": sum(response_times) / len(response_times) if response_times else 0,
            "session_duration": {
                "start": messages[0].timestamp.isoformat() if messages else None,
                "end": messages[-1].timestamp.isoformat() if messages else None
            }
        }
        
        return {
            "session_id": session_id,
            "analytics": analytics
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve session analytics: {str(e)}"
        )

@router.delete("/session/{session_id}")
async def delete_session(session_id: str, current_user = Depends(get_current_user)):
    """Delete a chat session and all its messages"""
    try:
        chat_service = get_chat_service()
        
        # Delete all messages in the session for the current user
        result = chat_service.message_service.collection.delete_many({
            "session_id": session_id,
            "user_id": str(current_user.id)
        })
        
        return {
            "message": f"Session {session_id} deleted successfully",
            "deleted_messages": result.deleted_count
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete session: {str(e)}"
        )

@router.get("/search")
async def search_messages(
    query: str,
    session_id: Optional[str] = None,
    current_user = Depends(get_current_user)
):
    """Search messages by content"""
    try:
        chat_service = get_chat_service()
        
        # Build search filter
        search_filter = {
            "user_id": str(current_user.id),
            "content": {"$regex": query, "$options": "i"}
        }
        
        if session_id:
            search_filter["session_id"] = session_id
        
        # Search messages
        messages = list(chat_service.message_service.collection.find(
            search_filter
        ).sort("timestamp", -1).limit(50))
        
        # Format results
        results = []
        for msg in messages:
            results.append({
                "id": str(msg["_id"]),
                "content": msg["content"],
                "message_type": msg["message_type"],
                "session_id": msg["session_id"],
                "timestamp": msg["timestamp"].isoformat(),
                "analytics": msg.get("analytics")
            })
        
        return {
            "query": query,
            "session_id": session_id,
            "results": results,
            "total": len(results)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search messages: {str(e)}"
        )
